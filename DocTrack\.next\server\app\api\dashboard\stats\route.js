"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_MGB_2_Workstation_Desktop_DocumentTracker_DocTrack_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/stats/route.ts */ \"(rsc)/./src/app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\DocTrack\\\\src\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_MGB_2_Workstation_Desktop_DocumentTracker_DocTrack_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/dashboard/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth/options */ \"(rsc)/./src/lib/auth/options.ts\");\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n/* harmony import */ var _services_stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/stats */ \"(rsc)/./src/services/stats/index.ts\");\n/* harmony import */ var _utils_cacheUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/cacheUtils */ \"(rsc)/./src/utils/cacheUtils.ts\");\n\n\n\n\n\n\n// Mark this route as dynamic to prevent static generation\nconst dynamic = \"force-dynamic\";\n// Cache duration in milliseconds (10 seconds - reduced for more frequent updates)\nconst CACHE_DURATION = 10 * 1000;\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const userId = session.user.id;\n        const now = Date.now();\n        // Check if force refresh is requested\n        const { searchParams } = new URL(request.url);\n        const forceRefresh = searchParams.get(\"forceRefresh\") === \"true\";\n        // Check cache first (unless force refresh is requested)\n        if (!forceRefresh) {\n            const cachedData = (0,_utils_cacheUtils__WEBPACK_IMPORTED_MODULE_5__.getUserStatsFromCache)(userId, now, CACHE_DURATION);\n            if (cachedData) {\n                console.log(`Using cached dashboard stats for user: ${userId}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(cachedData);\n            }\n        }\n        // Get fresh stats using DashboardStatsService\n        console.log(`Computing fresh dashboard stats for user: ${userId}`);\n        const dashboardData = await _services_stats__WEBPACK_IMPORTED_MODULE_4__.DashboardStatsService.getDashboardStats({\n            userId,\n            userRole: session.user.role,\n            userDivision: session.user.division\n        });\n        // Transform the data to match the expected frontend format\n        const response = {\n            stats: {\n                inbox: dashboardData.inboxDocuments,\n                pending: dashboardData.pendingDocuments,\n                processed: dashboardData.processedDocuments,\n                sent: dashboardData.sentDocuments,\n                forwarded: dashboardData.forwardedDocuments,\n                archived: dashboardData.archivedDocuments,\n                recent: dashboardData.recentDocuments.length\n            },\n            recentDocuments: dashboardData.recentDocuments\n        };\n        // Cache the result\n        (0,_utils_cacheUtils__WEBPACK_IMPORTED_MODULE_5__.setUserStatsInCache)(userId, response, now);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Error fetching dashboard stats:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to fetch dashboard stats\",\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth/options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.ts\");\n/* harmony import */ var _utils_audit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/audit */ \"(rsc)/./src/utils/audit.ts\");\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n/* harmony import */ var _utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/serverTimestamp */ \"(rsc)/./src/utils/serverTimestamp.ts\");\n/* harmony import */ var _utils_sessionToken__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/sessionToken */ \"(rsc)/./src/utils/sessionToken.ts\");\n\n\n\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            // Define the credentials that will be submitted from the login form\n            credentials: {\n                name: {\n                    label: \"Name\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials, req) {\n                // Add more detailed logging of received credentials\n                console.log(\"Received credentials:\", credentials);\n                if (!credentials?.name || !credentials?.password) {\n                    console.error(\"Missing credentials:\", {\n                        name: !!credentials?.name,\n                        password: !!credentials?.password\n                    });\n                    throw new Error(\"Name and password required\");\n                }\n                try {\n                    console.log(\"Looking up user with name:\", credentials.name);\n                    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findFirst({\n                        where: {\n                            name: {\n                                equals: credentials.name,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    });\n                    if (!user) {\n                        console.log(\"User not found with name:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"User found, checking password\");\n                    if (!user.password) {\n                        console.log(\"User has no password set\");\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    const isPasswordCorrect = await (0,bcrypt__WEBPACK_IMPORTED_MODULE_1__.compare)(credentials.password, user.password);\n                    if (!isPasswordCorrect) {\n                        console.log(\"Password incorrect for user:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"Authentication successful for user:\", credentials.name);\n                    const userId = user.id;\n                    // Create a new session token\n                    const userAgent = req?.headers?.[\"user-agent\"];\n                    const ipAddress = req?.headers?.[\"x-forwarded-for\"] || req?.socket?.remoteAddress || \"unknown\";\n                    const sessionToken = await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_6__.createSession)(userId, userAgent, ipAddress);\n                    // Log successful login\n                    await (0,_utils_audit__WEBPACK_IMPORTED_MODULE_3__.logAuditEvent)({\n                        action: _types_audit__WEBPACK_IMPORTED_MODULE_4__.AuditLogAction.USER_LOGIN,\n                        performedBy: userId,\n                        targetId: userId,\n                        targetType: \"User\",\n                        details: {\n                            name: user.name,\n                            email: user.email,\n                            role: user.role,\n                            division: user.division,\n                            sessionToken: sessionToken\n                        }\n                    });\n                    return {\n                        id: userId,\n                        name: user.name,\n                        email: user.email,\n                        role: user.role,\n                        division: user.division,\n                        image: user.image,\n                        sessionToken: sessionToken\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw new Error(\"Authentication failed. Please try again.\");\n                }\n                // This code is unreachable due to the try/catch block above\n                // but we'll keep it as a fallback\n                console.error(\"Warning: Reached unreachable code in NextAuth authorize callback\");\n                return null;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async jwt ({ token, user, trigger }) {\n            console.log(\"JWT callback called with user:\", user);\n            console.log(\"Initial token:\", token);\n            if (user) {\n                // Add user data to token\n                token.id = user.id;\n                // Cast user to any to access custom properties\n                const customUser = user;\n                token.name = customUser.name; // Ensure name is included\n                token.role = customUser.role;\n                token.division = customUser.division;\n                // Add session token to JWT\n                if (customUser.sessionToken) {\n                    token.sessionToken = customUser.sessionToken;\n                }\n                // Add server timestamp to token to invalidate sessions on server restart\n                token.serverTimestamp = (0,_utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_5__.getServerTimestamp)();\n                console.log(\"Updated token with user data:\", token);\n            }\n            // Handle sign out\n            if (trigger === \"signOut\") {\n                // Remove the session from the database when the user signs out\n                if (token.id && token.sessionToken) {\n                    await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_6__.removeSession)(token.id, token.sessionToken);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"Session callback called with token:\", token);\n            console.log(\"Initial session:\", session);\n            if (token) {\n                // Add user data to session\n                session.user.id = token.id;\n                session.user.name = token.name; // Ensure name is included\n                session.user.role = token.role;\n                session.user.division = token.division;\n                // Add server timestamp to session\n                session.serverTimestamp = token.serverTimestamp;\n                // Add session token to session\n                if (token.sessionToken) {\n                    session.sessionToken = token.sessionToken;\n                }\n            }\n            console.log(\"Returning session:\", session);\n            return session;\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/mongodb.ts":
/*!*******************************!*\
  !*** ./src/lib/db/mongodb.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ dbConnect)\n/* harmony export */ });\n// Temporary stub - this file was removed in favor of Prisma\n// This is just to prevent build errors while we migrate\nasync function dbConnect() {\n    // No-op - using Prisma instead\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL21vbmdvZGIudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDREQUE0RDtBQUM1RCx3REFBd0Q7QUFDekMsZUFBZUE7SUFDNUIsK0JBQStCO0lBQy9CLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvbGliL2RiL21vbmdvZGIudHM/MzBhNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUZW1wb3Jhcnkgc3R1YiAtIHRoaXMgZmlsZSB3YXMgcmVtb3ZlZCBpbiBmYXZvciBvZiBQcmlzbWFcbi8vIFRoaXMgaXMganVzdCB0byBwcmV2ZW50IGJ1aWxkIGVycm9ycyB3aGlsZSB3ZSBtaWdyYXRlXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBkYkNvbm5lY3QoKSB7XG4gIC8vIE5vLW9wIC0gdXNpbmcgUHJpc21hIGluc3RlYWRcbiAgcmV0dXJuIHRydWU7XG59XG4iXSwibmFtZXMiOlsiZGJDb25uZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/prisma.ts":
/*!******************************!*\
  !*** ./src/lib/db/prisma.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO0tBQVE7QUFDaEIsR0FBRTtBQUVKLElBQUlDLElBQXlCLEVBQWNKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvbGliL2RiL3ByaXNtYS50cz9iODkyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknXSxcbiAgfSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Document.ts":
/*!********************************!*\
  !*** ./src/models/Document.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Temporary stub - this file was removed in favor of Prisma\n// This is just to prevent build errors while we migrate\n// Temporary stub - use Prisma instead\nconst Document = {\n    findById: ()=>Promise.resolve(null),\n    findOne: ()=>Promise.resolve(null),\n    find: ()=>Promise.resolve([]),\n    findMany: ()=>Promise.resolve([]),\n    create: ()=>Promise.resolve(null),\n    findByIdAndUpdate: ()=>Promise.resolve(null),\n    deleteOne: ()=>Promise.resolve(null),\n    deleteMany: ()=>Promise.resolve(null),\n    countDocuments: ()=>Promise.resolve(0),\n    aggregate: ()=>Promise.resolve([])\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Document);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Document.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/core/statsClient.ts":
/*!************************************************!*\
  !*** ./src/services/stats/core/statsClient.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsClient: () => (/* binding */ StatsClient)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/services/stats/core/types.ts\");\n/**\n * Stats Client\n * Core database operations and common queries for statistics\n * Simplified version using Prisma ORM - returns basic stats\n */ \n\nclass StatsClient {\n    /**\n   * Get total unique documents count by tracking number\n   * Simplified version - returns 0 since we don't have documents table yet\n   */ static async getTotalUniqueDocuments(query = {}) {\n        // TODO: Implement when documents table is added to Prisma schema\n        return 0;\n    }\n    /**\n   * Get documents by division with deduplication\n   * Simplified version - returns default counts since we don't have documents table yet\n   */ static async getDocumentsByDivision(query = {}) {\n        // TODO: Implement when documents table is added to Prisma schema\n        return {\n            ..._types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_DIVISION_COUNTS\n        };\n    }\n    /**\n   * Get documents by status with deduplication and user responsibility filtering\n   * Simplified version - returns default counts since we don't have documents table yet\n   */ static async getDocumentsByStatus(query = {}) {\n        // TODO: Implement when documents table is added to Prisma schema\n        return {\n            ..._types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_STATUS_COUNTS\n        };\n    }\n    /**\n   * Get documents by status with user responsibility filtering\n   * Simplified version - returns default counts since we don't have documents table yet\n   */ static async getDocumentsByStatusForUser(userId, userRole, userDivision) {\n        // TODO: Implement when documents table is added to Prisma schema\n        return {\n            ..._types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_STATUS_COUNTS\n        };\n    }\n    /**\n   * Get users by division using Prisma\n   */ static async getUsersByDivision() {\n        try {\n            const users = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.users.groupBy({\n                by: [\n                    \"division\"\n                ],\n                _count: {\n                    id: true\n                }\n            });\n            const result = {\n                ..._types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_DIVISION_COUNTS\n            };\n            users.forEach((item)=>{\n                if (item.division && item.division in result) {\n                    const division = item.division;\n                    result[division] = item._count.id;\n                }\n            });\n            return result;\n        } catch (error) {\n            console.error(\"Error getting users by division:\", error);\n            return {\n                ..._types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_DIVISION_COUNTS\n            };\n        }\n    }\n    /**\n   * Get total users count using Prisma\n   */ static async getTotalUsers() {\n        try {\n            return await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.users.count();\n        } catch (error) {\n            console.error(\"Error getting total users:\", error);\n            return 0;\n        }\n    }\n    /**\n   * Build query based on user context and role\n   * Simplified version - returns empty query since we don't have documents\n   */ static buildUserContextQuery(context) {\n        // TODO: Implement when documents table is added to Prisma schema\n        return {};\n    }\n    /**\n   * Get recent documents for user\n   * Simplified version - returns empty array since we don't have documents table yet\n   */ static async getRecentDocumentsForUser(userId, userRole, userDivision, limit = 10) {\n        // TODO: Implement when documents table is added to Prisma schema\n        return [];\n    }\n    /**\n   * Get unique documents with journey information\n   * Simplified version - returns empty array since we don't have documents table yet\n   */ static async getUniqueDocumentsWithJourney(query = {}, limit) {\n        // TODO: Implement when documents table is added to Prisma schema\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/core/statsClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/core/types.ts":
/*!******************************************!*\
  !*** ./src/services/stats/core/types.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_DIVISION_COUNTS: () => (/* binding */ DEFAULT_DIVISION_COUNTS),\n/* harmony export */   DEFAULT_STATUS_COUNTS: () => (/* binding */ DEFAULT_STATUS_COUNTS),\n/* harmony export */   STATS_CONFIG: () => (/* binding */ STATS_CONFIG)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Stats Service Types\n * Centralized type definitions for all statistics operations\n */ \nconst STATS_CONFIG = {\n    defaultLimit: 10,\n    maxDateRange: 365,\n    cacheTimeout: 5 * 60 * 1000 // 5 minutes\n};\nconst DEFAULT_DIVISION_COUNTS = {\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.ORD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.FAD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.MMD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.MSESDD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.GSD]: 0\n};\nconst DEFAULT_STATUS_COUNTS = {\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.INBOX]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.SENT]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PENDING]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PROCESSED]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.FORWARDED]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.RECEIVED]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.ARCHIVED]: 0\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/core/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/index.ts":
/*!*************************************!*\
  !*** ./src/services/stats/index.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminStatsService: () => (/* reexport safe */ _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__.AdminStatsService),\n/* harmony export */   AggregationHelpers: () => (/* reexport safe */ _utils_aggregationHelpers__WEBPACK_IMPORTED_MODULE_7__.AggregationHelpers),\n/* harmony export */   DEFAULT_DIVISION_COUNTS: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_DIVISION_COUNTS),\n/* harmony export */   DEFAULT_STATUS_COUNTS: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_STATUS_COUNTS),\n/* harmony export */   DashboardStatsService: () => (/* reexport safe */ _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__.DashboardStatsService),\n/* harmony export */   ProcessingStatsService: () => (/* reexport safe */ _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__.ProcessingStatsService),\n/* harmony export */   ReportStatsService: () => (/* reexport safe */ _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__.ReportStatsService),\n/* harmony export */   STATS_CONFIG: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.STATS_CONFIG),\n/* harmony export */   StatsClient: () => (/* reexport safe */ _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient),\n/* harmony export */   VolumeStatsService: () => (/* reexport safe */ _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__.VolumeStatsService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAdminStats: () => (/* binding */ getAdminStats),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getDocumentVolumeStats: () => (/* binding */ getDocumentVolumeStats),\n/* harmony export */   getProcessingTimeStats: () => (/* binding */ getProcessingTimeStats),\n/* harmony export */   getVolumeReportStats: () => (/* binding */ getVolumeReportStats)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _core_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core/types */ \"(rsc)/./src/services/stats/core/types.ts\");\n/* harmony import */ var _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/adminStatsService */ \"(rsc)/./src/services/stats/services/adminStatsService.ts\");\n/* harmony import */ var _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/dashboardStatsService */ \"(rsc)/./src/services/stats/services/dashboardStatsService.ts\");\n/* harmony import */ var _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./services/volumeStatsService */ \"(rsc)/./src/services/stats/services/volumeStatsService.ts\");\n/* harmony import */ var _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./services/processingStatsService */ \"(rsc)/./src/services/stats/services/processingStatsService.ts\");\n/* harmony import */ var _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./services/reportStatsService */ \"(rsc)/./src/services/stats/services/reportStatsService.ts\");\n/* harmony import */ var _utils_aggregationHelpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/aggregationHelpers */ \"(rsc)/./src/services/stats/utils/aggregationHelpers.ts\");\n/**\n * Stats Services - Main Export\n * Centralized export for all statistics services and utilities\n */ // Core Infrastructure\n\n\n// Specialized Services\n\n\n\n\n\n// Utilities\n\n// Legacy compatibility wrapper (deprecated)\n\n\n\n\n\n/**\n * @deprecated Use AdminStatsService.getAdminStats() instead\n */ const getAdminStats = async ()=>{\n    console.warn(\"getAdminStats is deprecated. Use AdminStatsService.getAdminStats() instead.\");\n    return _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__.AdminStatsService.getAdminStats();\n};\n/**\n * @deprecated Use DashboardStatsService.getDashboardStats() instead\n */ const getDashboardStats = async (context)=>{\n    console.warn(\"getDashboardStats is deprecated. Use DashboardStatsService.getDashboardStats() instead.\");\n    return _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__.DashboardStatsService.getDashboardStats(context);\n};\n/**\n * @deprecated Use VolumeStatsService.getDocumentVolumeStats() instead\n */ const getDocumentVolumeStats = async (startDate, endDate, division)=>{\n    console.warn(\"getDocumentVolumeStats is deprecated. Use VolumeStatsService.getDocumentVolumeStats() instead.\");\n    return _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__.VolumeStatsService.getDocumentVolumeStats(startDate, endDate, division);\n};\n/**\n * @deprecated Use ProcessingStatsService.getProcessingTimeStats() instead\n */ const getProcessingTimeStats = async (startDate, endDate)=>{\n    console.warn(\"getProcessingTimeStats is deprecated. Use ProcessingStatsService.getProcessingTimeStats() instead.\");\n    return _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__.ProcessingStatsService.getProcessingTimeStats(startDate, endDate);\n};\n/**\n * @deprecated Use ReportStatsService.getVolumeReport() instead\n */ const getVolumeReportStats = async (filters)=>{\n    console.warn(\"getVolumeReportStats is deprecated. Use ReportStatsService.getVolumeReport() instead.\");\n    return _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__.ReportStatsService.getVolumeReport(filters);\n};\n// Default export for convenience\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    // Services\n    AdminStatsService: _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__.AdminStatsService,\n    DashboardStatsService: _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__.DashboardStatsService,\n    VolumeStatsService: _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__.VolumeStatsService,\n    ProcessingStatsService: _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__.ProcessingStatsService,\n    ReportStatsService: _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__.ReportStatsService,\n    // Legacy functions (deprecated)\n    getAdminStats,\n    getDashboardStats,\n    getDocumentVolumeStats,\n    getProcessingTimeStats,\n    getVolumeReportStats\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/adminStatsService.ts":
/*!**********************************************************!*\
  !*** ./src/services/stats/services/adminStatsService.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminStatsService: () => (/* binding */ AdminStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/**\n * Admin Stats Service\n * Handles administrative dashboard statistics\n */ \nclass AdminStatsService {\n    /**\n   * Get comprehensive admin statistics\n   * @returns Admin dashboard statistics\n   */ static async getAdminStats() {\n        try {\n            // Get all statistics in parallel for better performance\n            const [totalDocuments, documentsByDivision, totalUsers, usersByDivision] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByDivision(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUsers(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getUsersByDivision()\n            ]);\n            return {\n                totalDocuments,\n                documentsByDivision,\n                totalUsers,\n                usersByDivision\n            };\n        } catch (error) {\n            console.error(\"Error getting admin stats:\", error);\n            throw new Error(\"Failed to retrieve admin statistics\");\n        }\n    }\n    /**\n   * Get admin statistics with date range filter\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Filtered admin statistics\n   */ static async getAdminStatsWithDateRange(startDate, endDate) {\n        try {\n            let query = {};\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            const [totalDocuments, documentsByDivision, totalUsers, usersByDivision] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByDivision(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUsers(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getUsersByDivision()\n            ]);\n            return {\n                totalDocuments,\n                documentsByDivision,\n                totalUsers,\n                usersByDivision\n            };\n        } catch (error) {\n            console.error(\"Error getting admin stats with date range:\", error);\n            throw new Error(\"Failed to retrieve filtered admin statistics\");\n        }\n    }\n    /**\n   * Get admin statistics summary for quick overview\n   * @returns Simplified admin statistics\n   */ static async getAdminStatsSummary() {\n        try {\n            const stats = await this.getAdminStats();\n            // Find most and least active divisions\n            const divisionEntries = Object.entries(stats.documentsByDivision);\n            const mostActive = divisionEntries.reduce((max, current)=>current[1] > max[1] ? current : max);\n            const leastActive = divisionEntries.reduce((min, current)=>current[1] < min[1] ? current : min);\n            return {\n                totalDocuments: stats.totalDocuments,\n                totalUsers: stats.totalUsers,\n                mostActiveDivision: mostActive[0],\n                leastActiveDivision: leastActive[0]\n            };\n        } catch (error) {\n            console.error(\"Error getting admin stats summary:\", error);\n            throw new Error(\"Failed to retrieve admin statistics summary\");\n        }\n    }\n    /**\n   * Get division performance comparison\n   * @returns Division performance metrics\n   */ static async getDivisionPerformance() {\n        try {\n            const stats = await this.getAdminStats();\n            const performance = Object.keys(stats.documentsByDivision).map((division)=>{\n                const documentCount = stats.documentsByDivision[division];\n                const userCount = stats.usersByDivision[division];\n                const documentsPerUser = userCount > 0 ? documentCount / userCount : 0;\n                let efficiency = \"low\";\n                if (documentsPerUser > 10) efficiency = \"high\";\n                else if (documentsPerUser > 5) efficiency = \"medium\";\n                return {\n                    division,\n                    documentCount,\n                    userCount,\n                    documentsPerUser: Math.round(documentsPerUser * 100) / 100,\n                    efficiency\n                };\n            });\n            return performance.sort((a, b)=>b.documentsPerUser - a.documentsPerUser);\n        } catch (error) {\n            console.error(\"Error getting division performance:\", error);\n            throw new Error(\"Failed to retrieve division performance metrics\");\n        }\n    }\n    /**\n   * Get system health metrics\n   * @returns System health indicators\n   */ static async getSystemHealth() {\n        try {\n            const stats = await this.getAdminStats();\n            const averageDocumentsPerUser = stats.totalUsers > 0 ? stats.totalDocuments / stats.totalUsers : 0;\n            const activeDivisions = Object.values(stats.documentsByDivision).filter((count)=>count > 0).length;\n            const recommendations = [];\n            let status = \"healthy\";\n            // Health checks\n            if (stats.totalUsers === 0) {\n                status = \"critical\";\n                recommendations.push(\"No users found in the system\");\n            } else if (stats.totalUsers < 5) {\n                status = \"warning\";\n                recommendations.push(\"Low user count - consider user onboarding\");\n            }\n            if (averageDocumentsPerUser < 1) {\n                status = \"warning\";\n                recommendations.push(\"Low document activity - encourage document creation\");\n            }\n            if (activeDivisions < 3) {\n                status = \"warning\";\n                recommendations.push(\"Some divisions have no document activity\");\n            }\n            if (recommendations.length === 0) {\n                recommendations.push(\"System is operating normally\");\n            }\n            return {\n                status,\n                metrics: {\n                    totalDocuments: stats.totalDocuments,\n                    totalUsers: stats.totalUsers,\n                    averageDocumentsPerUser: Math.round(averageDocumentsPerUser * 100) / 100,\n                    activeDivisions\n                },\n                recommendations\n            };\n        } catch (error) {\n            console.error(\"Error getting system health:\", error);\n            throw new Error(\"Failed to retrieve system health metrics\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/adminStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/dashboardStatsService.ts":
/*!**************************************************************!*\
  !*** ./src/services/stats/services/dashboardStatsService.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStatsService: () => (/* binding */ DashboardStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Dashboard Stats Service\n * Handles user dashboard statistics and metrics\n */ \n\nclass DashboardStatsService {\n    /**\n   * Get dashboard statistics for a specific user\n   * @param context User context (userId, role, division)\n   * @returns User-specific dashboard statistics\n   */ static async getDashboardStats(context) {\n        try {\n            const { userId, userRole, userDivision } = context;\n            // Build query based on user role and permissions\n            const query = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context);\n            // Get all statistics in parallel for better performance\n            const [totalDocuments, statusCounts] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatusForUser(userId, userRole, userDivision)\n            ]);\n            // Simplified version - return empty array for recent documents\n            const recentDocuments = [];\n            return {\n                totalDocuments,\n                pendingDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING],\n                processedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED],\n                archivedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.ARCHIVED],\n                sentDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.SENT],\n                forwardedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.FORWARDED],\n                inboxDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX],\n                recentDocuments\n            };\n        } catch (error) {\n            console.error(\"Error getting dashboard stats:\", error);\n            throw new Error(\"Failed to retrieve dashboard statistics\");\n        }\n    }\n    /**\n   * Get dashboard statistics with date range filter\n   * @param context User context\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Filtered dashboard statistics\n   */ static async getDashboardStatsWithDateRange(context, startDate, endDate) {\n        try {\n            let query = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context);\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            const [totalDocuments, statusCounts, recentDocuments] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatus(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getRecentDocuments(query, 10, context.userId) // Pass userId to get user-specific actions\n            ]);\n            return {\n                totalDocuments,\n                pendingDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING],\n                processedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED],\n                archivedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.ARCHIVED],\n                sentDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.SENT],\n                forwardedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.FORWARDED],\n                inboxDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX],\n                recentDocuments\n            };\n        } catch (error) {\n            console.error(\"Error getting dashboard stats with date range:\", error);\n            throw new Error(\"Failed to retrieve filtered dashboard statistics\");\n        }\n    }\n    /**\n   * Get user productivity metrics\n   * @param context User context\n   * @returns User productivity statistics\n   */ static async getUserProductivityMetrics(context) {\n        try {\n            const { userId } = context;\n            // Simplified version - return default values since we don't have documents table yet\n            const documentsCreated = 0;\n            const documentsProcessed = 0;\n            // Calculate productivity score (simple formula)\n            const productivityScore = Math.min(100, (documentsCreated * 2 + documentsProcessed) * 5);\n            // Get trend (simplified - compare last 7 days vs previous 7 days)\n            const now = new Date();\n            const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            const fourteenDaysAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);\n            const [recentActivity, previousActivity] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments({\n                    ...createdQuery,\n                    createdAt: {\n                        $gte: sevenDaysAgo\n                    }\n                }),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments({\n                    ...createdQuery,\n                    createdAt: {\n                        $gte: fourteenDaysAgo,\n                        $lt: sevenDaysAgo\n                    }\n                })\n            ]);\n            let trend = \"stable\";\n            if (recentActivity > previousActivity) trend = \"increasing\";\n            else if (recentActivity < previousActivity) trend = \"decreasing\";\n            return {\n                documentsCreated,\n                documentsProcessed,\n                averageProcessingTime: 0,\n                productivityScore,\n                trend\n            };\n        } catch (error) {\n            console.error(\"Error getting user productivity metrics:\", error);\n            throw new Error(\"Failed to retrieve user productivity metrics\");\n        }\n    }\n    /**\n   * Get dashboard summary for quick overview\n   * @param context User context\n   * @returns Simplified dashboard summary\n   */ static async getDashboardSummary(context) {\n        try {\n            const stats = await this.getDashboardStats(context);\n            const pendingActions = stats.pendingDocuments + stats.inboxDocuments;\n            // Get documents completed today\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            const tomorrow = new Date(today);\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            const todayQuery = {\n                ..._core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context),\n                status: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED,\n                updatedAt: {\n                    $gte: today,\n                    $lt: tomorrow\n                }\n            };\n            const completedToday = await _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(todayQuery);\n            // Determine priority based on pending actions\n            let priority = \"low\";\n            if (pendingActions > 10) priority = \"high\";\n            else if (pendingActions > 5) priority = \"medium\";\n            // Suggest next action\n            let nextAction = \"All caught up!\";\n            if (stats.inboxDocuments > 0) {\n                nextAction = `Review ${stats.inboxDocuments} document(s) in inbox`;\n            } else if (stats.pendingDocuments > 0) {\n                nextAction = `Process ${stats.pendingDocuments} pending document(s)`;\n            }\n            return {\n                totalDocuments: stats.totalDocuments,\n                pendingActions,\n                completedToday,\n                priority,\n                nextAction\n            };\n        } catch (error) {\n            console.error(\"Error getting dashboard summary:\", error);\n            throw new Error(\"Failed to retrieve dashboard summary\");\n        }\n    }\n    /**\n   * Get workload distribution for the user\n   * @param context User context\n   * @returns Workload distribution metrics\n   */ static async getWorkloadDistribution(context) {\n        try {\n            const query = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context);\n            const [statusCounts, categoryCounts] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatus(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByCategory(query)\n            ]);\n            const totalWorkload = Object.values(statusCounts).reduce((sum, count)=>sum + count, 0);\n            let workloadLevel = \"light\";\n            if (totalWorkload > 20) workloadLevel = \"heavy\";\n            else if (totalWorkload > 10) workloadLevel = \"moderate\";\n            const recommendations = [];\n            if (workloadLevel === \"heavy\") {\n                recommendations.push(\"Consider prioritizing urgent documents\");\n                recommendations.push(\"Delegate tasks if possible\");\n            } else if (workloadLevel === \"moderate\") {\n                recommendations.push(\"Maintain current pace\");\n                recommendations.push(\"Focus on pending documents\");\n            } else {\n                recommendations.push(\"Good workload balance\");\n                recommendations.push(\"Consider taking on additional responsibilities\");\n            }\n            return {\n                byStatus: statusCounts,\n                byCategory: categoryCounts,\n                workloadLevel,\n                recommendations\n            };\n        } catch (error) {\n            console.error(\"Error getting workload distribution:\", error);\n            throw new Error(\"Failed to retrieve workload distribution\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/dashboardStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/processingStatsService.ts":
/*!***************************************************************!*\
  !*** ./src/services/stats/services/processingStatsService.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProcessingStatsService: () => (/* binding */ ProcessingStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Processing Stats Service\n * Handles document processing time analytics and performance metrics\n */ \n\nclass ProcessingStatsService {\n    /**\n   * Get comprehensive processing time statistics\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Processing time statistics\n   */ static async getProcessingTimeStats(startDate, endDate) {\n        try {\n            // Build query for documents with journey data\n            let query = {\n                journey: {\n                    $exists: true,\n                    $ne: []\n                }\n            };\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            // Get documents with journey data\n            const documents = await _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsWithJourney(query);\n            // Calculate processing times\n            const averageInboxTime = await this.calculateAverageProcessingTime(documents, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING);\n            const averagePendingTime = await this.calculateAverageProcessingTime(documents, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED);\n            const averageTotalTime = await this.calculateTotalProcessingTime(documents);\n            // Get processing times by category\n            const processingTimesByCategory = await this.getProcessingTimesByCategory(documents);\n            // Get processing times by division\n            const processingTimesByDivision = await this.getProcessingTimesByDivision(documents);\n            return {\n                averageInboxTime,\n                averagePendingTime,\n                averageTotalTime,\n                processingTimesByCategory,\n                processingTimesByDivision\n            };\n        } catch (error) {\n            console.error(\"Error getting processing time stats:\", error);\n            throw new Error(\"Failed to retrieve processing time statistics\");\n        }\n    }\n    /**\n   * Calculate average processing time between two statuses\n   * @param documents Documents with journey data\n   * @param fromStatus Starting status\n   * @param toStatus Ending status\n   * @returns Average processing time in hours\n   */ static async calculateAverageProcessingTime(documents, fromStatus, toStatus) {\n        const processingTimes = [];\n        documents.forEach((doc)=>{\n            if (!doc.journey || doc.journey.length === 0) return;\n            const fromEntry = doc.journey.find((entry)=>entry.status === fromStatus);\n            const toEntry = doc.journey.find((entry)=>entry.status === toStatus);\n            if (fromEntry && toEntry && fromEntry.timestamp && toEntry.timestamp) {\n                const timeDiff = new Date(toEntry.timestamp).getTime() - new Date(fromEntry.timestamp).getTime();\n                const hours = timeDiff / (1000 * 60 * 60); // Convert to hours\n                if (hours >= 0) {\n                    processingTimes.push(hours);\n                }\n            }\n        });\n        if (processingTimes.length === 0) return 0;\n        const average = processingTimes.reduce((sum, time)=>sum + time, 0) / processingTimes.length;\n        return Math.round(average * 100) / 100; // Round to 2 decimal places\n    }\n    /**\n   * Calculate total processing time from creation to completion\n   * @param documents Documents with journey data\n   * @returns Average total processing time in hours\n   */ static async calculateTotalProcessingTime(documents) {\n        const totalTimes = [];\n        documents.forEach((doc)=>{\n            if (!doc.journey || doc.journey.length === 0) return;\n            // Find first and last entries in journey\n            const sortedJourney = doc.journey.sort((a, b)=>new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());\n            const firstEntry = sortedJourney[0];\n            const lastEntry = sortedJourney[sortedJourney.length - 1];\n            if (firstEntry && lastEntry && firstEntry.timestamp && lastEntry.timestamp) {\n                const timeDiff = new Date(lastEntry.timestamp).getTime() - new Date(firstEntry.timestamp).getTime();\n                const hours = timeDiff / (1000 * 60 * 60);\n                if (hours >= 0) {\n                    totalTimes.push(hours);\n                }\n            }\n        });\n        if (totalTimes.length === 0) return 0;\n        const average = totalTimes.reduce((sum, time)=>sum + time, 0) / totalTimes.length;\n        return Math.round(average * 100) / 100;\n    }\n    /**\n   * Get processing times grouped by category\n   * @param documents Documents with journey data\n   * @returns Processing times by category\n   */ static async getProcessingTimesByCategory(documents) {\n        const categoryGroups = {};\n        // Group documents by category\n        documents.forEach((doc)=>{\n            const category = doc.category || \"Unknown\";\n            if (!categoryGroups[category]) {\n                categoryGroups[category] = [];\n            }\n            categoryGroups[category].push(doc);\n        });\n        // Calculate processing times for each category\n        const results = await Promise.all(Object.entries(categoryGroups).map(async ([category, docs])=>{\n            const avgInboxTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING);\n            const avgPendingTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED);\n            const avgTotalTime = await this.calculateTotalProcessingTime(docs);\n            return {\n                category,\n                avgInboxTime,\n                avgPendingTime,\n                avgTotalTime\n            };\n        }));\n        return results.sort((a, b)=>b.avgTotalTime - a.avgTotalTime);\n    }\n    /**\n   * Get processing times grouped by division\n   * @param documents Documents with journey data\n   * @returns Processing times by division\n   */ static async getProcessingTimesByDivision(documents) {\n        const divisionGroups = {};\n        // Group documents by current location (division)\n        documents.forEach((doc)=>{\n            const division = doc.currentLocation || \"Unknown\";\n            if (!divisionGroups[division]) {\n                divisionGroups[division] = [];\n            }\n            divisionGroups[division].push(doc);\n        });\n        // Calculate processing times for each division\n        const results = await Promise.all(Object.entries(divisionGroups).map(async ([division, docs])=>{\n            const avgInboxTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING);\n            const avgPendingTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED);\n            const avgTotalTime = await this.calculateTotalProcessingTime(docs);\n            return {\n                division,\n                avgInboxTime,\n                avgPendingTime,\n                avgTotalTime\n            };\n        }));\n        return results.sort((a, b)=>b.avgTotalTime - a.avgTotalTime);\n    }\n    /**\n   * Get processing efficiency metrics\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Processing efficiency analysis\n   */ static async getProcessingEfficiency(startDate, endDate) {\n        try {\n            const stats = await this.getProcessingTimeStats(startDate, endDate);\n            // Calculate overall efficiency (lower average time = higher efficiency)\n            const maxExpectedTime = 72; // 72 hours (3 days) as benchmark\n            const efficiency = Math.max(0, Math.min(100, (maxExpectedTime - stats.averageTotalTime) / maxExpectedTime * 100));\n            // Identify bottlenecks\n            const bottlenecks = [];\n            if (stats.averageInboxTime > 24) {\n                bottlenecks.push(\"Inbox processing takes too long\");\n            }\n            if (stats.averagePendingTime > 48) {\n                bottlenecks.push(\"Pending status duration is excessive\");\n            }\n            // Generate recommendations\n            const recommendations = [];\n            if (efficiency < 50) {\n                recommendations.push(\"Consider process automation\");\n                recommendations.push(\"Review workflow bottlenecks\");\n            } else if (efficiency < 75) {\n                recommendations.push(\"Optimize pending document handling\");\n                recommendations.push(\"Implement priority queues\");\n            } else {\n                recommendations.push(\"Maintain current efficiency levels\");\n                recommendations.push(\"Consider best practice sharing\");\n            }\n            // Find fastest and slowest categories\n            const sortedCategories = stats.processingTimesByCategory.sort((a, b)=>a.avgTotalTime - b.avgTotalTime);\n            const fastestCategory = sortedCategories[0]?.category || \"N/A\";\n            const slowestCategory = sortedCategories[sortedCategories.length - 1]?.category || \"N/A\";\n            return {\n                efficiency: Math.round(efficiency * 100) / 100,\n                bottlenecks,\n                recommendations,\n                fastestCategory,\n                slowestCategory\n            };\n        } catch (error) {\n            console.error(\"Error getting processing efficiency:\", error);\n            throw new Error(\"Failed to retrieve processing efficiency metrics\");\n        }\n    }\n    /**\n   * Get processing time trends\n   * @param days Number of days to analyze\n   * @returns Processing time trend analysis\n   */ static async getProcessingTimeTrends(days = 30) {\n        try {\n            const now = new Date();\n            const periodStart = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);\n            const previousPeriodStart = new Date(now.getTime() - days * 2 * 24 * 60 * 60 * 1000);\n            // Get current and previous period stats\n            const [currentStats, previousStats] = await Promise.all([\n                this.getProcessingTimeStats(periodStart, now),\n                this.getProcessingTimeStats(previousPeriodStart, periodStart)\n            ]);\n            const currentAverage = currentStats.averageTotalTime;\n            const previousAverage = previousStats.averageTotalTime;\n            // Calculate change percentage (negative means improvement)\n            const changePercentage = previousAverage > 0 ? (currentAverage - previousAverage) / previousAverage * 100 : 0;\n            // Determine trend\n            let trend = \"stable\";\n            if (changePercentage < -10) trend = \"improving\";\n            else if (changePercentage > 10) trend = \"declining\";\n            return {\n                currentAverage: Math.round(currentAverage * 100) / 100,\n                previousAverage: Math.round(previousAverage * 100) / 100,\n                trend,\n                changePercentage: Math.round(changePercentage * 100) / 100\n            };\n        } catch (error) {\n            console.error(\"Error getting processing time trends:\", error);\n            throw new Error(\"Failed to retrieve processing time trends\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/processingStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/reportStatsService.ts":
/*!***********************************************************!*\
  !*** ./src/services/stats/services/reportStatsService.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReportStatsService: () => (/* binding */ ReportStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Report Stats Service\n * Handles volume reporting with advanced filtering and analytics\n */ \n\nclass ReportStatsService {\n    /**\n   * Get comprehensive volume report with filters\n   * @param filters Report filters and parameters\n   * @returns Volume report statistics\n   */ static async getVolumeReport(filters) {\n        try {\n            const { startDate, endDate, division, category, userId, groupBy = \"day\", userRole, userDivision, currentUserId } = filters;\n            // Build base query\n            let query = this.buildReportQuery(filters);\n            // Apply user context restrictions\n            const userContext = {\n                userId: currentUserId,\n                userRole,\n                userDivision\n            };\n            if (userRole !== \"REGIONAL_DIRECTOR\") {\n                const contextQuery = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(userContext);\n                query = {\n                    $and: [\n                        query,\n                        contextQuery\n                    ]\n                };\n            }\n            // Get all report data in parallel\n            const [totalDocuments, byStatus, byDivision, byCategory, timeSeries, topUsers] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatus(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByDivision(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByCategory(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTimeSeriesData(query, groupBy),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTopUsers(query, 10)\n            ]);\n            return {\n                totalDocuments,\n                totalPending: byStatus[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING] || 0,\n                totalProcessed: byStatus[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED] || 0,\n                byStatus,\n                byDivision,\n                byCategory,\n                timeSeries,\n                topUsers\n            };\n        } catch (error) {\n            console.error(\"Error getting volume report:\", error);\n            throw new Error(\"Failed to generate volume report\");\n        }\n    }\n    /**\n   * Build query based on report filters\n   * @param filters Report filters\n   * @returns MongoDB query object\n   */ static buildReportQuery(filters) {\n        const { startDate, endDate, division, category, userId } = filters;\n        let query = {};\n        // Date range filter\n        if (startDate && endDate) {\n            query.createdAt = {\n                $gte: new Date(startDate),\n                $lte: new Date(endDate)\n            };\n        }\n        // Division filter\n        if (division && division !== \"all\") {\n            query.currentLocation = division;\n        }\n        // Category filter\n        if (category && category !== \"all\") {\n            query.category = category;\n        }\n        // User filter\n        if (userId && userId !== \"all\") {\n            query.$or = [\n                {\n                    createdBy: userId\n                },\n                {\n                    recipientId: userId\n                }\n            ];\n        }\n        return query;\n    }\n    /**\n   * Get detailed report with advanced analytics\n   * @param filters Report filters\n   * @returns Detailed report with insights\n   */ static async getDetailedReport(filters) {\n        try {\n            const summary = await this.getVolumeReport(filters);\n            // Generate insights\n            const insights = await this.generateReportInsights(summary, filters);\n            // Analyze performance\n            const performance = await this.analyzeReportPerformance(summary, filters);\n            return {\n                summary,\n                insights,\n                performance\n            };\n        } catch (error) {\n            console.error(\"Error getting detailed report:\", error);\n            throw new Error(\"Failed to generate detailed report\");\n        }\n    }\n    /**\n   * Generate insights from report data\n   * @param reportData Report statistics\n   * @param filters Original filters\n   * @returns Report insights\n   */ static async generateReportInsights(reportData, filters) {\n        const trends = [];\n        const anomalies = [];\n        const recommendations = [];\n        // Analyze time series for trends\n        const timeSeriesEntries = Object.entries(reportData.timeSeries);\n        if (timeSeriesEntries.length > 1) {\n            const firstPeriod = timeSeriesEntries[0];\n            const lastPeriod = timeSeriesEntries[timeSeriesEntries.length - 1];\n            const firstTotal = Object.values(firstPeriod[1]).reduce((sum, count)=>sum + (count || 0), 0);\n            const lastTotal = Object.values(lastPeriod[1]).reduce((sum, count)=>sum + (count || 0), 0);\n            if (lastTotal > firstTotal * 1.2) {\n                trends.push(\"Document volume is increasing significantly\");\n            } else if (lastTotal < firstTotal * 0.8) {\n                trends.push(\"Document volume is decreasing\");\n            } else {\n                trends.push(\"Document volume is stable\");\n            }\n        }\n        // Analyze status distribution\n        const pendingRatio = reportData.totalDocuments > 0 ? reportData.totalPending / reportData.totalDocuments : 0;\n        if (pendingRatio > 0.3) {\n            anomalies.push(\"High percentage of pending documents detected\");\n            recommendations.push(\"Review pending document processing workflow\");\n        }\n        // Analyze division distribution\n        const divisionEntries = Object.entries(reportData.byDivision);\n        const maxDivisionVolume = Math.max(...divisionEntries.map(([, count])=>count));\n        const minDivisionVolume = Math.min(...divisionEntries.map(([, count])=>count));\n        if (maxDivisionVolume > minDivisionVolume * 3) {\n            anomalies.push(\"Significant workload imbalance between divisions\");\n            recommendations.push(\"Consider redistributing workload across divisions\");\n        }\n        // Analyze top users\n        if (reportData.topUsers.length > 0) {\n            const topUser = reportData.topUsers[0];\n            const averageUserVolume = reportData.totalDocuments / reportData.topUsers.length;\n            if (topUser.count > averageUserVolume * 2) {\n                anomalies.push(`User ${topUser.name} has significantly higher document volume`);\n                recommendations.push(\"Monitor high-volume users for potential burnout\");\n            }\n        }\n        // General recommendations\n        if (reportData.totalDocuments === 0) {\n            recommendations.push(\"No documents found for the selected criteria\");\n        } else if (reportData.totalDocuments < 10) {\n            recommendations.push(\"Low document volume - consider expanding date range\");\n        }\n        return {\n            trends,\n            anomalies,\n            recommendations\n        };\n    }\n    /**\n   * Analyze performance metrics from report data\n   * @param reportData Report statistics\n   * @param filters Original filters\n   * @returns Performance analysis\n   */ static async analyzeReportPerformance(reportData, filters) {\n        const bottlenecks = [];\n        const topPerformers = [];\n        // Calculate efficiency based on processed vs pending ratio\n        const processedRatio = reportData.totalDocuments > 0 ? reportData.totalProcessed / reportData.totalDocuments : 0;\n        const efficiency = Math.round(processedRatio * 100);\n        // Identify bottlenecks\n        if (reportData.totalPending > reportData.totalProcessed) {\n            bottlenecks.push(\"More documents pending than processed\");\n        }\n        const inboxCount = reportData.byStatus[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX] || 0;\n        if (inboxCount > reportData.totalDocuments * 0.2) {\n            bottlenecks.push(\"High number of documents in inbox\");\n        }\n        // Identify top performers\n        reportData.topUsers.slice(0, 3).forEach((user)=>{\n            topPerformers.push(`${user.name} (${user.division}): ${user.count} documents`);\n        });\n        return {\n            efficiency,\n            bottlenecks,\n            topPerformers\n        };\n    }\n    /**\n   * Export report data in various formats\n   * @param filters Report filters\n   * @param format Export format\n   * @returns Formatted report data\n   */ static async exportReport(filters, format = \"json\") {\n        try {\n            const reportData = await this.getVolumeReport(filters);\n            switch(format){\n                case \"csv\":\n                    return this.formatReportAsCSV(reportData);\n                case \"summary\":\n                    return this.formatReportAsSummary(reportData);\n                default:\n                    return reportData;\n            }\n        } catch (error) {\n            console.error(\"Error exporting report:\", error);\n            throw new Error(\"Failed to export report\");\n        }\n    }\n    /**\n   * Format report data as CSV\n   * @param reportData Report statistics\n   * @returns CSV formatted string\n   */ static formatReportAsCSV(reportData) {\n        const headers = [\n            \"Metric\",\n            \"Value\"\n        ];\n        const rows = [\n            [\n                \"Total Documents\",\n                reportData.totalDocuments.toString()\n            ],\n            [\n                \"Total Pending\",\n                reportData.totalPending.toString()\n            ],\n            [\n                \"Total Processed\",\n                reportData.totalProcessed.toString()\n            ],\n            ...Object.entries(reportData.byStatus).map(([status, count])=>[\n                    status,\n                    count.toString()\n                ]),\n            ...Object.entries(reportData.byDivision).map(([division, count])=>[\n                    division,\n                    count.toString()\n                ]),\n            ...Object.entries(reportData.byCategory).map(([category, count])=>[\n                    category,\n                    count.toString()\n                ])\n        ];\n        return [\n            headers,\n            ...rows\n        ].map((row)=>row.join(\",\")).join(\"\\n\");\n    }\n    /**\n   * Format report data as summary\n   * @param reportData Report statistics\n   * @returns Summary object\n   */ static formatReportAsSummary(reportData) {\n        return {\n            overview: {\n                totalDocuments: reportData.totalDocuments,\n                pendingDocuments: reportData.totalPending,\n                processedDocuments: reportData.totalProcessed,\n                completionRate: reportData.totalDocuments > 0 ? Math.round(reportData.totalProcessed / reportData.totalDocuments * 100) : 0\n            },\n            topDivision: Object.entries(reportData.byDivision).reduce((max, current)=>current[1] > max[1] ? current : max, [\n                \"\",\n                0\n            ]),\n            topCategory: Object.entries(reportData.byCategory).reduce((max, current)=>current[1] > max[1] ? current : max, [\n                \"\",\n                0\n            ]),\n            topUser: reportData.topUsers[0] || null\n        };\n    }\n    /**\n   * Get report comparison between two periods\n   * @param currentFilters Current period filters\n   * @param previousFilters Previous period filters\n   * @returns Comparison analysis\n   */ static async getReportComparison(currentFilters, previousFilters) {\n        try {\n            const [current, previous] = await Promise.all([\n                this.getVolumeReport(currentFilters),\n                this.getVolumeReport(previousFilters)\n            ]);\n            const volumeChange = previous.totalDocuments > 0 ? (current.totalDocuments - previous.totalDocuments) / previous.totalDocuments * 100 : 0;\n            const pendingChange = previous.totalPending > 0 ? (current.totalPending - previous.totalPending) / previous.totalPending * 100 : 0;\n            const processedChange = previous.totalProcessed > 0 ? (current.totalProcessed - previous.totalProcessed) / previous.totalProcessed * 100 : 0;\n            // Determine overall trend\n            let trend = \"stable\";\n            if (processedChange > 10 && pendingChange < -10) trend = \"improving\";\n            else if (processedChange < -10 || pendingChange > 10) trend = \"declining\";\n            return {\n                current,\n                previous,\n                comparison: {\n                    volumeChange: Math.round(volumeChange * 100) / 100,\n                    pendingChange: Math.round(pendingChange * 100) / 100,\n                    processedChange: Math.round(processedChange * 100) / 100,\n                    trend\n                }\n            };\n        } catch (error) {\n            console.error(\"Error getting report comparison:\", error);\n            throw new Error(\"Failed to generate report comparison\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/reportStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/volumeStatsService.ts":
/*!***********************************************************!*\
  !*** ./src/services/stats/services/volumeStatsService.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VolumeStatsService: () => (/* binding */ VolumeStatsService)\n/* harmony export */ });\n/* harmony import */ var _models_Document__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/Document */ \"(rsc)/./src/models/Document.ts\");\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/**\n * Volume Stats Service\n * Handles document volume analytics and trends\n */ \n\nclass VolumeStatsService {\n    /**\n   * Get comprehensive document volume statistics\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @param division Optional division filter\n   * @returns Document volume statistics\n   */ static async getDocumentVolumeStats(startDate, endDate, division) {\n        try {\n            // Build base query\n            let query = {};\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            if (division) {\n                query.currentLocation = division;\n            }\n            // Get all volume statistics in parallel\n            const [totalVolume, volumeByPeriod, volumeByDivision, volumeByCategory] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getTotalUniqueDocuments(query),\n                this.getVolumeByPeriod(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByDivision(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByCategory(query)\n            ]);\n            return {\n                totalVolume,\n                volumeByPeriod,\n                volumeByDivision,\n                volumeByCategory\n            };\n        } catch (error) {\n            console.error(\"Error getting document volume stats:\", error);\n            throw new Error(\"Failed to retrieve document volume statistics\");\n        }\n    }\n    /**\n   * Get volume by time period (daily for last 30 days)\n   * @param query Base query filter\n   * @returns Volume data by period\n   */ static async getVolumeByPeriod(query) {\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        const volumeByPeriod = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: {\n                    ...query,\n                    createdAt: {\n                        $gte: thirtyDaysAgo\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: {\n                        trackingNumber: \"$trackingNumber\",\n                        date: {\n                            $dateToString: {\n                                format: \"%Y-%m-%d\",\n                                date: \"$createdAt\"\n                            }\n                        }\n                    },\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$_id.date\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    _id: 1\n                }\n            },\n            {\n                $project: {\n                    period: \"$_id\",\n                    count: 1,\n                    _id: 0\n                }\n            }\n        ]);\n        return volumeByPeriod;\n    }\n    /**\n   * Get volume trends and growth analysis\n   * @param dateRange Date range for analysis\n   * @returns Volume trend analysis\n   */ static async getVolumeTrends(dateRange) {\n        try {\n            const now = new Date();\n            const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n            const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);\n            // Get current period (last 30 days) and previous period (30-60 days ago)\n            const [currentPeriod, previousPeriod] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getTotalUniqueDocuments({\n                    createdAt: {\n                        $gte: thirtyDaysAgo,\n                        $lte: now\n                    }\n                }),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getTotalUniqueDocuments({\n                    createdAt: {\n                        $gte: sixtyDaysAgo,\n                        $lt: thirtyDaysAgo\n                    }\n                })\n            ]);\n            // Calculate growth rate\n            const growthRate = previousPeriod > 0 ? (currentPeriod - previousPeriod) / previousPeriod * 100 : 0;\n            // Determine trend\n            let trend = \"stable\";\n            if (growthRate > 5) trend = \"increasing\";\n            else if (growthRate < -5) trend = \"decreasing\";\n            // Simple forecast (next 30 days based on current trend)\n            const forecast = Math.max(0, Math.round(currentPeriod * (1 + growthRate / 100)));\n            return {\n                currentPeriod,\n                previousPeriod,\n                growthRate: Math.round(growthRate * 100) / 100,\n                trend,\n                forecast\n            };\n        } catch (error) {\n            console.error(\"Error getting volume trends:\", error);\n            throw new Error(\"Failed to retrieve volume trends\");\n        }\n    }\n    /**\n   * Get peak volume analysis\n   * @param days Number of days to analyze\n   * @returns Peak volume analysis\n   */ static async getPeakVolumeAnalysis(days = 30) {\n        try {\n            const startDate = new Date();\n            startDate.setDate(startDate.getDate() - days);\n            const dailyVolumes = await this.getVolumeByPeriod({\n                createdAt: {\n                    $gte: startDate\n                }\n            });\n            if (dailyVolumes.length === 0) {\n                return {\n                    peakDay: \"N/A\",\n                    peakVolume: 0,\n                    averageVolume: 0,\n                    lowDay: \"N/A\",\n                    lowVolume: 0,\n                    recommendations: [\n                        \"No data available for the specified period\"\n                    ]\n                };\n            }\n            // Find peak and low days\n            const peakDay = dailyVolumes.reduce((max, current)=>current.count > max.count ? current : max);\n            const lowDay = dailyVolumes.reduce((min, current)=>current.count < min.count ? current : min);\n            const averageVolume = dailyVolumes.reduce((sum, day)=>sum + day.count, 0) / dailyVolumes.length;\n            // Generate recommendations\n            const recommendations = [];\n            if (peakDay.count > averageVolume * 2) {\n                recommendations.push(\"Consider load balancing during peak days\");\n                recommendations.push(\"Prepare additional resources for high-volume periods\");\n            }\n            if (lowDay.count < averageVolume * 0.5) {\n                recommendations.push(\"Investigate reasons for low-volume days\");\n                recommendations.push(\"Consider redistributing workload from peak days\");\n            }\n            if (recommendations.length === 0) {\n                recommendations.push(\"Volume distribution appears balanced\");\n            }\n            return {\n                peakDay: peakDay.period,\n                peakVolume: peakDay.count,\n                averageVolume: Math.round(averageVolume * 100) / 100,\n                lowDay: lowDay.period,\n                lowVolume: lowDay.count,\n                recommendations\n            };\n        } catch (error) {\n            console.error(\"Error getting peak volume analysis:\", error);\n            throw new Error(\"Failed to retrieve peak volume analysis\");\n        }\n    }\n    /**\n   * Get division volume comparison\n   * @param dateRange Optional date range filter\n   * @returns Division volume comparison\n   */ static async getDivisionVolumeComparison(dateRange) {\n        try {\n            let query = {};\n            if (dateRange?.startDate && dateRange?.endDate) {\n                query.createdAt = {\n                    $gte: dateRange.startDate,\n                    $lte: dateRange.endDate\n                };\n            }\n            const volumeByDivision = await _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByDivision(query);\n            const totalVolume = Object.values(volumeByDivision).reduce((sum, count)=>sum + count, 0);\n            const averageVolume = totalVolume / Object.keys(volumeByDivision).length;\n            const comparison = Object.entries(volumeByDivision).map(([division, volume])=>({\n                    division,\n                    volume,\n                    percentage: totalVolume > 0 ? Math.round(volume / totalVolume * 10000) / 100 : 0,\n                    rank: 0,\n                    status: volume > averageVolume * 1.1 ? \"above_average\" : volume < averageVolume * 0.9 ? \"below_average\" : \"average\"\n                })).sort((a, b)=>b.volume - a.volume).map((item, index)=>({\n                    ...item,\n                    rank: index + 1\n                }));\n            return comparison;\n        } catch (error) {\n            console.error(\"Error getting division volume comparison:\", error);\n            throw new Error(\"Failed to retrieve division volume comparison\");\n        }\n    }\n    /**\n   * Get category volume distribution\n   * @param division Optional division filter\n   * @param dateRange Optional date range filter\n   * @returns Category volume distribution\n   */ static async getCategoryVolumeDistribution(division, dateRange) {\n        try {\n            let query = {};\n            if (division) {\n                query.currentLocation = division;\n            }\n            if (dateRange?.startDate && dateRange?.endDate) {\n                query.createdAt = {\n                    $gte: dateRange.startDate,\n                    $lte: dateRange.endDate\n                };\n            }\n            const volumeByCategory = await _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByCategory(query);\n            const totalVolume = Object.values(volumeByCategory).reduce((sum, count)=>sum + count, 0);\n            // Get previous period for trend analysis\n            const previousQuery = {\n                ...query\n            };\n            if (dateRange?.startDate && dateRange?.endDate) {\n                const periodLength = dateRange.endDate.getTime() - dateRange.startDate.getTime();\n                const previousStart = new Date(dateRange.startDate.getTime() - periodLength);\n                const previousEnd = new Date(dateRange.endDate.getTime() - periodLength);\n                previousQuery.createdAt = {\n                    $gte: previousStart,\n                    $lte: previousEnd\n                };\n            }\n            const previousVolumeByCategory = await _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByCategory(previousQuery);\n            const distribution = Object.entries(volumeByCategory).map(([category, volume])=>{\n                const percentage = totalVolume > 0 ? Math.round(volume / totalVolume * 10000) / 100 : 0;\n                const previousVolume = previousVolumeByCategory[category] || 0;\n                let trend = \"stable\";\n                if (volume > previousVolume * 1.1) trend = \"up\";\n                else if (volume < previousVolume * 0.9) trend = \"down\";\n                return {\n                    category,\n                    volume,\n                    percentage,\n                    trend\n                };\n            }).sort((a, b)=>b.volume - a.volume);\n            return distribution;\n        } catch (error) {\n            console.error(\"Error getting category volume distribution:\", error);\n            throw new Error(\"Failed to retrieve category volume distribution\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/volumeStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/utils/aggregationHelpers.ts":
/*!********************************************************!*\
  !*** ./src/services/stats/utils/aggregationHelpers.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AggregationHelpers: () => (/* binding */ AggregationHelpers)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Aggregation Helpers\n * Common MongoDB aggregation pipelines and utilities for statistics\n */ \nclass AggregationHelpers {\n    /**\n   * Create aggregation pipeline for document deduplication by tracking number\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createDeduplicationPipeline(additionalMatch = {}) {\n        return [\n            {\n                $match: additionalMatch\n            },\n            {\n                $group: {\n                    _id: \"$trackingNumber\",\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $replaceRoot: {\n                    newRoot: \"$doc\"\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for grouping by division\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createDivisionGroupingPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$currentLocation\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for grouping by status\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createStatusGroupingPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$status\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for grouping by category\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createCategoryGroupingPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$category\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for time series data\n   * @param additionalMatch Additional match conditions\n   * @param groupBy Time grouping ('day', 'week', 'month')\n   * @returns Aggregation pipeline\n   */ static createTimeSeriesPipeline(additionalMatch = {}, groupBy = \"day\") {\n        let dateFormat;\n        switch(groupBy){\n            case \"week\":\n                dateFormat = \"%Y-W%U\";\n                break;\n            case \"month\":\n                dateFormat = \"%Y-%m\";\n                break;\n            default:\n                dateFormat = \"%Y-%m-%d\";\n        }\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: {\n                        date: {\n                            $dateToString: {\n                                format: dateFormat,\n                                date: \"$createdAt\"\n                            }\n                        },\n                        status: \"$status\"\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"_id.date\": 1\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for top users\n   * @param additionalMatch Additional match conditions\n   * @param limit Number of top users to return\n   * @returns Aggregation pipeline\n   */ static createTopUsersPipeline(additionalMatch = {}, limit = 10) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$createdBy\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    count: -1\n                }\n            },\n            {\n                $limit: limit\n            },\n            {\n                $lookup: {\n                    from: \"users\",\n                    localField: \"_id\",\n                    foreignField: \"_id\",\n                    as: \"userInfo\"\n                }\n            },\n            {\n                $project: {\n                    userId: {\n                        $toString: \"$_id\"\n                    },\n                    count: 1,\n                    name: {\n                        $arrayElemAt: [\n                            \"$userInfo.name\",\n                            0\n                        ]\n                    },\n                    division: {\n                        $arrayElemAt: [\n                            \"$userInfo.division\",\n                            0\n                        ]\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for user statistics\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createUserStatsPipeline(additionalMatch = {}) {\n        return [\n            {\n                $match: additionalMatch\n            },\n            {\n                $group: {\n                    _id: \"$division\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create match query for user context\n   * @param userId User ID\n   * @param userRole User role\n   * @param userDivision User division\n   * @returns Match query object\n   */ static createUserContextMatch(userId, userRole, userDivision) {\n        if (userRole === \"REGIONAL_DIRECTOR\") {\n            return {}; // Regional Director can see all documents\n        } else if (userRole === \"DIVISION_CHIEF\") {\n            return {\n                $or: [\n                    {\n                        currentLocation: userDivision\n                    },\n                    {\n                        recipientId: userId\n                    }\n                ]\n            };\n        } else {\n            return {\n                $or: [\n                    {\n                        createdBy: userId\n                    },\n                    {\n                        recipientId: userId\n                    }\n                ]\n            };\n        }\n    }\n    /**\n   * Create date range match query\n   * @param startDate Start date\n   * @param endDate End date\n   * @param dateField Field to apply date filter (default: 'createdAt')\n   * @returns Date range match query\n   */ static createDateRangeMatch(startDate, endDate, dateField = \"createdAt\") {\n        if (!startDate && !endDate) return {};\n        const dateQuery = {};\n        if (startDate && endDate) {\n            dateQuery[dateField] = {\n                $gte: startDate,\n                $lte: endDate\n            };\n        } else if (startDate) {\n            dateQuery[dateField] = {\n                $gte: startDate\n            };\n        } else if (endDate) {\n            dateQuery[dateField] = {\n                $lte: endDate\n            };\n        }\n        return dateQuery;\n    }\n    /**\n   * Create aggregation pipeline for processing time analysis\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createProcessingTimePipeline(additionalMatch = {}) {\n        return [\n            {\n                $match: {\n                    ...additionalMatch,\n                    journey: {\n                        $exists: true,\n                        $ne: []\n                    }\n                }\n            },\n            {\n                $addFields: {\n                    journeyArray: {\n                        $map: {\n                            input: \"$journey\",\n                            as: \"entry\",\n                            in: {\n                                status: \"$$entry.status\",\n                                timestamp: {\n                                    $toDate: \"$$entry.timestamp\"\n                                }\n                            }\n                        }\n                    }\n                }\n            },\n            {\n                $addFields: {\n                    sortedJourney: {\n                        $sortArray: {\n                            input: \"$journeyArray\",\n                            sortBy: {\n                                timestamp: 1\n                            }\n                        }\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for volume trends\n   * @param days Number of days to analyze\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createVolumeTrendsPipeline(days = 30, additionalMatch = {}) {\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        return [\n            {\n                $match: {\n                    ...additionalMatch,\n                    createdAt: {\n                        $gte: startDate\n                    }\n                }\n            },\n            ...this.createDeduplicationPipeline(),\n            {\n                $group: {\n                    _id: {\n                        $dateToString: {\n                            format: \"%Y-%m-%d\",\n                            date: \"$createdAt\"\n                        }\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    _id: 1\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for efficiency metrics\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createEfficiencyMetricsPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: null,\n                    totalDocuments: {\n                        $sum: 1\n                    },\n                    pendingCount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        \"$status\",\n                                        _types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PENDING\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    processedCount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        \"$status\",\n                                        _types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PROCESSED\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    archivedCount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        \"$status\",\n                                        _types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.ARCHIVED\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    }\n                }\n            },\n            {\n                $addFields: {\n                    completionRate: {\n                        $cond: [\n                            {\n                                $gt: [\n                                    \"$totalDocuments\",\n                                    0\n                                ]\n                            },\n                            {\n                                $multiply: [\n                                    {\n                                        $divide: [\n                                            {\n                                                $add: [\n                                                    \"$processedCount\",\n                                                    \"$archivedCount\"\n                                                ]\n                                            },\n                                            \"$totalDocuments\"\n                                        ]\n                                    },\n                                    100\n                                ]\n                            },\n                            0\n                        ]\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for workload distribution\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createWorkloadDistributionPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $facet: {\n                    byStatus: [\n                        {\n                            $group: {\n                                _id: \"$status\",\n                                count: {\n                                    $sum: 1\n                                }\n                            }\n                        }\n                    ],\n                    byDivision: [\n                        {\n                            $group: {\n                                _id: \"$currentLocation\",\n                                count: {\n                                    $sum: 1\n                                }\n                            }\n                        }\n                    ],\n                    byCategory: [\n                        {\n                            $group: {\n                                _id: \"$category\",\n                                count: {\n                                    $sum: 1\n                                }\n                            }\n                        }\n                    ]\n                }\n            }\n        ];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/utils/aggregationHelpers.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/audit.ts":
/*!****************************!*\
  !*** ./src/types/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditLogAction: () => (/* binding */ AuditLogAction)\n/* harmony export */ });\nvar AuditLogAction;\n(function(AuditLogAction) {\n    AuditLogAction[\"USER_CREATED\"] = \"USER_CREATED\";\n    AuditLogAction[\"USER_UPDATED\"] = \"USER_UPDATED\";\n    AuditLogAction[\"USER_DELETED\"] = \"USER_DELETED\";\n    AuditLogAction[\"USER_LOGIN\"] = \"USER_LOGIN\";\n    AuditLogAction[\"DOCUMENT_CREATED\"] = \"DOCUMENT_CREATED\";\n    AuditLogAction[\"DOCUMENT_UPDATED\"] = \"DOCUMENT_UPDATED\";\n    AuditLogAction[\"DOCUMENT_DELETED\"] = \"DOCUMENT_DELETED\";\n    AuditLogAction[\"DOCUMENT_STATUS_CHANGED\"] = \"DOCUMENT_STATUS_CHANGED\";\n    AuditLogAction[\"DOCUMENT_SHARED\"] = \"DOCUMENT_SHARED\";\n    AuditLogAction[\"DOCUMENT_FORWARDED\"] = \"DOCUMENT_FORWARDED\";\n    AuditLogAction[\"DOCUMENT_RECEIVED\"] = \"DOCUMENT_RECEIVED\";\n    AuditLogAction[\"DOCUMENT_PROCESSED\"] = \"DOCUMENT_PROCESSED\";\n    AuditLogAction[\"PROFILE_CHANGE_REQUESTED\"] = \"PROFILE_CHANGE_REQUESTED\";\n    AuditLogAction[\"PROFILE_CHANGE_APPROVED\"] = \"PROFILE_CHANGE_APPROVED\";\n    AuditLogAction[\"PROFILE_CHANGE_REJECTED\"] = \"PROFILE_CHANGE_REJECTED\";\n    AuditLogAction[\"FILE_UPLOADED\"] = \"FILE_UPLOADED\";\n    AuditLogAction[\"FILE_DOWNLOADED\"] = \"FILE_DOWNLOADED\";\n    AuditLogAction[\"FILE_DELETED\"] = \"FILE_DELETED\";\n    AuditLogAction[\"FILE_ACCESSED\"] = \"FILE_ACCESSED\";\n    AuditLogAction[\"FEEDBACK_CREATED\"] = \"FEEDBACK_CREATED\";\n    AuditLogAction[\"FEEDBACK_UPDATED\"] = \"FEEDBACK_UPDATED\";\n    AuditLogAction[\"FEEDBACK_DELETED\"] = \"FEEDBACK_DELETED\";\n    AuditLogAction[\"FEEDBACK_STATUS_CHANGED\"] = \"FEEDBACK_STATUS_CHANGED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_GENERATED\"] = \"FEEDBACK_AI_SUGGESTION_GENERATED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_REMOVED\"] = \"FEEDBACK_AI_SUGGESTION_REMOVED\";\n    AuditLogAction[\"SEARCH_PERFORMED\"] = \"SEARCH_PERFORMED\";\n    AuditLogAction[\"DATA_CLEANUP\"] = \"DATA_CLEANUP\";\n    AuditLogAction[\"SYSTEM_MAINTENANCE\"] = \"SYSTEM_MAINTENANCE\";\n    AuditLogAction[\"ARCHIVE_EXPORTED\"] = \"ARCHIVE_EXPORTED\";\n})(AuditLogAction || (AuditLogAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Division: () => (/* binding */ Division),\n/* harmony export */   DocumentAction: () => (/* binding */ DocumentAction),\n/* harmony export */   DocumentCategory: () => (/* binding */ DocumentCategory),\n/* harmony export */   DocumentStatus: () => (/* binding */ DocumentStatus),\n/* harmony export */   FeedbackCategory: () => (/* binding */ FeedbackCategory),\n/* harmony export */   FeedbackStatus: () => (/* binding */ FeedbackStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n// User Roles\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n    UserRole[\"REGIONAL_DIRECTOR\"] = \"REGIONAL_DIRECTOR\";\n    UserRole[\"DIVISION_CHIEF\"] = \"DIVISION_CHIEF\";\n    UserRole[\"EMPLOYEE\"] = \"EMPLOYEE\";\n})(UserRole || (UserRole = {}));\nvar Division;\n(function(Division) {\n    Division[\"ORD\"] = \"ORD\";\n    Division[\"FAD\"] = \"FAD\";\n    Division[\"MMD\"] = \"MMD\";\n    Division[\"MSESDD\"] = \"MSESDD\";\n    Division[\"GSD\"] = \"GSD\";\n})(Division || (Division = {}));\nvar DocumentStatus;\n(function(DocumentStatus) {\n    DocumentStatus[\"INBOX\"] = \"INBOX\";\n    DocumentStatus[\"SENT\"] = \"SENT\";\n    DocumentStatus[\"RECEIVED\"] = \"RECEIVED\";\n    DocumentStatus[\"FORWARDED\"] = \"FORWARDED\";\n    DocumentStatus[\"PENDING\"] = \"PENDING\";\n    DocumentStatus[\"PROCESSED\"] = \"PROCESSED\";\n    DocumentStatus[\"ARCHIVED\"] = \"ARCHIVED\"; // Document has been archived for storage\n})(DocumentStatus || (DocumentStatus = {}));\nvar DocumentCategory;\n(function(DocumentCategory) {\n    // Common Office Documents\n    DocumentCategory[\"MEMO\"] = \"MEMO\";\n    DocumentCategory[\"LETTER\"] = \"LETTER\";\n    DocumentCategory[\"REPORT\"] = \"REPORT\";\n    DocumentCategory[\"PROPOSAL\"] = \"PROPOSAL\";\n    DocumentCategory[\"MINUTES\"] = \"MINUTES\";\n    DocumentCategory[\"FORM\"] = \"FORM\";\n    // Official Documents\n    DocumentCategory[\"CIRCULAR\"] = \"CIRCULAR\";\n    DocumentCategory[\"ADVISORY\"] = \"ADVISORY\";\n    DocumentCategory[\"BULLETIN\"] = \"BULLETIN\";\n    DocumentCategory[\"NOTICE\"] = \"NOTICE\";\n    DocumentCategory[\"ANNOUNCEMENT\"] = \"ANNOUNCEMENT\";\n    DocumentCategory[\"RESOLUTION\"] = \"RESOLUTION\";\n    DocumentCategory[\"POLICY\"] = \"POLICY\";\n    DocumentCategory[\"GUIDELINE\"] = \"GUIDELINE\";\n    DocumentCategory[\"DIRECTIVE\"] = \"DIRECTIVE\";\n    DocumentCategory[\"MEMORANDUM_ORDER\"] = \"MEMORANDUM ORDER\";\n    DocumentCategory[\"MEMORANDUM_CIRCULAR\"] = \"MEMORANDUM CIRCULAR\";\n    DocumentCategory[\"EXECUTIVE_ORDER\"] = \"EXECUTIVE ORDER\";\n    DocumentCategory[\"ADMINISTRATIVE_ORDER\"] = \"ADMINISTRATIVE ORDER\";\n    // Legal & Financial Documents\n    DocumentCategory[\"CONTRACT\"] = \"CONTRACT\";\n    DocumentCategory[\"CERTIFICATE\"] = \"CERTIFICATE\";\n    DocumentCategory[\"ENDORSEMENT\"] = \"ENDORSEMENT\";\n    DocumentCategory[\"MANUAL\"] = \"MANUAL\";\n    DocumentCategory[\"INVOICE\"] = \"INVOICE\";\n    DocumentCategory[\"RECEIPT\"] = \"RECEIPT\";\n    DocumentCategory[\"VOUCHER\"] = \"VOUCHER\";\n    DocumentCategory[\"REQUISITION\"] = \"REQUISITION\";\n    DocumentCategory[\"PURCHASE_ORDER\"] = \"PURCHASE ORDER\";\n    DocumentCategory[\"BUDGET_REQUEST\"] = \"BUDGET REQUEST\";\n    DocumentCategory[\"TRAVEL_ORDER\"] = \"TRAVEL ORDER\";\n    DocumentCategory[\"LEAVE_FORM\"] = \"LEAVE FORM\";\n    // Other\n    DocumentCategory[\"OTHER\"] = \"OTHER\";\n})(DocumentCategory || (DocumentCategory = {}));\nvar DocumentAction;\n(function(DocumentAction) {\n    DocumentAction[\"NONE\"] = \"No specific action required\";\n    // Actions from the image (A-S)\n    DocumentAction[\"FOR_INFO\"] = \"A - For information/guidance/reference\";\n    DocumentAction[\"FOR_COMMENTS\"] = \"B - For comments/recommendations\";\n    DocumentAction[\"TAKE_UP\"] = \"C - Pls. take up with me\";\n    DocumentAction[\"DRAFT_ANSWER\"] = \"D - Pls. draft answer/memo/acknow.\";\n    DocumentAction[\"FOR_ACTION\"] = \"E - For appropriate action\";\n    DocumentAction[\"IMMEDIATE_INVESTIGATION\"] = \"F - Pls. immediate investigation\";\n    DocumentAction[\"ATTACH_SUPPORTING\"] = \"G - Pls. attach supporting papers\";\n    DocumentAction[\"FOR_APPROVAL\"] = \"H - For approval\";\n    DocumentAction[\"FOR_SIGNATURE\"] = \"I - For initial/signature\";\n    DocumentAction[\"STUDY_EVALUATE\"] = \"J - Pls. study / evaluate\";\n    DocumentAction[\"RELEASE_FILE\"] = \"K - Pls. release/file\";\n    DocumentAction[\"UPDATE_STATUS\"] = \"L - Update status of case\";\n    DocumentAction[\"FILE_CLOSE\"] = \"M - Filed / Close\";\n    DocumentAction[\"FOR_ADA\"] = \"N - For ADA / Check Preparation\";\n    DocumentAction[\"FOR_DISCUSSION\"] = \"O - FOD (For Discussion)\";\n    DocumentAction[\"FOR_REVISION\"] = \"P - For Revision\";\n    DocumentAction[\"ATTACH_DRAFT\"] = \"Q - Pls. Attach Draft File\";\n    DocumentAction[\"SAVED\"] = \"R - Saved\";\n    DocumentAction[\"FOR_SCANNING\"] = \"S - For Scanning\";\n    // Additional useful actions for office work\n    DocumentAction[\"URGENT\"] = \"URGENT - Requires immediate attention\";\n    DocumentAction[\"CONFIDENTIAL\"] = \"CONFIDENTIAL - Restricted access\";\n    DocumentAction[\"FOR_REVIEW\"] = \"FOR REVIEW - Please review and provide feedback\";\n    DocumentAction[\"FOR_COORDINATION\"] = \"FOR COORDINATION - Coordinate with relevant departments\";\n    DocumentAction[\"FOR_COMPLIANCE\"] = \"FOR COMPLIANCE - Ensure compliance with regulations\";\n    DocumentAction[\"FOR_IMPLEMENTATION\"] = \"FOR IMPLEMENTATION - Implement the described actions\";\n    DocumentAction[\"FOR_FILING\"] = \"FOR FILING - File for future reference\";\n    DocumentAction[\"FOR_DISTRIBUTION\"] = \"FOR DISTRIBUTION - Distribute to concerned parties\";\n    DocumentAction[\"FOR_ENDORSEMENT\"] = \"FOR ENDORSEMENT - Endorse to appropriate authority\";\n    DocumentAction[\"FOR_VERIFICATION\"] = \"FOR VERIFICATION - Verify information/data\";\n    DocumentAction[\"FOR_RECORDING\"] = \"FOR RECORDING - Record in the system\";\n})(DocumentAction || (DocumentAction = {}));\nvar FeedbackCategory;\n(function(FeedbackCategory) {\n    FeedbackCategory[\"BUG\"] = \"bug\";\n    FeedbackCategory[\"FEATURE\"] = \"feature\";\n    FeedbackCategory[\"IMPROVEMENT\"] = \"improvement\";\n    FeedbackCategory[\"OTHER\"] = \"other\";\n})(FeedbackCategory || (FeedbackCategory = {}));\nvar FeedbackStatus;\n(function(FeedbackStatus) {\n    FeedbackStatus[\"PENDING\"] = \"pending\";\n    FeedbackStatus[\"REVIEWED\"] = \"reviewed\";\n    FeedbackStatus[\"IMPLEMENTED\"] = \"implemented\";\n    FeedbackStatus[\"REJECTED\"] = \"rejected\";\n})(FeedbackStatus || (FeedbackStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/audit.ts":
/*!****************************!*\
  !*** ./src/utils/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logAuditEvent: () => (/* binding */ logAuditEvent)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.ts\");\n\nasync function logAuditEvent({ action, performedBy, targetId, targetType, details, request }) {\n    try {\n        const auditLogData = {\n            action,\n            userId: performedBy,\n            details: JSON.stringify({\n                targetId,\n                targetType,\n                details\n            })\n        };\n        // Add request information if available\n        if (request) {\n            auditLogData.ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n            auditLogData.userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        }\n        // Create the audit log entry\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.auditLog.create({\n            data: auditLogData\n        });\n    } catch (error) {\n        console.error(\"Error creating audit log:\", error);\n    // Don't throw the error - we don't want to break the main functionality\n    // if audit logging fails\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/cacheUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/cacheUtils.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAllStatsCache: () => (/* binding */ clearAllStatsCache),\n/* harmony export */   clearDashboardCache: () => (/* binding */ clearDashboardCache),\n/* harmony export */   clearUserStatsCache: () => (/* binding */ clearUserStatsCache),\n/* harmony export */   forceDashboardRefresh: () => (/* binding */ forceDashboardRefresh),\n/* harmony export */   getUserStatsFromCache: () => (/* binding */ getUserStatsFromCache),\n/* harmony export */   refreshDashboardAfterAction: () => (/* binding */ refreshDashboardAfterAction),\n/* harmony export */   setUserStatsInCache: () => (/* binding */ setUserStatsInCache)\n/* harmony export */ });\n/**\n * Utility functions for managing cache in the application\n */ // Server-side cache for dashboard stats\nconst statsCache = new Map();\n/**\n * Function to clear the server-side cache for a specific user\n * @param userId The ID of the user whose cache should be cleared\n */ function clearUserStatsCache(userId) {\n    const cacheKey = `dashboard_stats_${userId}`;\n    statsCache.delete(cacheKey);\n    console.log(`Cleared dashboard stats cache for user: ${userId}`);\n}\n/**\n * Clear all server-side cache\n */ function clearAllStatsCache() {\n    statsCache.clear();\n    console.log(\"Cleared all dashboard stats cache\");\n}\n/**\n * Get a value from the server-side stats cache\n * @param userId The user ID to get stats for\n * @param timestamp The current timestamp\n * @param cacheDuration The cache duration in milliseconds\n * @returns The cached data or null if not found or expired\n */ function getUserStatsFromCache(userId, timestamp, cacheDuration) {\n    const cacheKey = `dashboard_stats_${userId}`;\n    const cachedData = statsCache.get(cacheKey);\n    // Return cached data if it's still valid\n    if (cachedData && timestamp - cachedData.timestamp < cacheDuration) {\n        return cachedData.data;\n    }\n    return null;\n}\n/**\n * Set a value in the server-side stats cache\n * @param userId The user ID to set stats for\n * @param data The data to cache\n * @param timestamp The current timestamp\n */ function setUserStatsInCache(userId, data, timestamp) {\n    const cacheKey = `dashboard_stats_${userId}`;\n    statsCache.set(cacheKey, {\n        data,\n        timestamp\n    });\n}\n// Client-side cache functions\n/**\n * Clear dashboard stats cache from localStorage\n */ function clearDashboardCache() {\n    try {\n        localStorage.removeItem(\"dashboard_stats_cache\");\n        localStorage.removeItem(\"dashboard_stats_expiry\");\n        console.log(\"Dashboard cache cleared from localStorage\");\n    } catch (error) {\n        console.error(\"Error clearing dashboard cache:\", error);\n    }\n}\n/**\n * Force refresh dashboard stats by clearing cache and making a new request\n */ async function forceDashboardRefresh() {\n    try {\n        // Clear local storage cache\n        clearDashboardCache();\n        // Make a direct API call to refresh server-side cache\n        const response = await fetch(\"/api/dashboard/stats?forceRefresh=true\", {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache\",\n                \"Pragma\": \"no-cache\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to refresh dashboard: ${response.status}`);\n        }\n        console.log(\"Dashboard data refreshed successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"Error refreshing dashboard:\", error);\n        return false;\n    }\n}\n/**\n * Trigger a dashboard refresh after document actions\n * @param delay Optional delay in milliseconds before refreshing\n */ function refreshDashboardAfterAction(delay = 500) {\n    // Clear cache immediately\n    clearDashboardCache();\n    // Refresh after a short delay to allow server-side changes to propagate\n    setTimeout(()=>{\n        forceDashboardRefresh().then((success)=>{\n            if (success) {\n                console.log(\"Dashboard refreshed after document action\");\n            }\n        }).catch((error)=>{\n            console.error(\"Failed to refresh dashboard after action:\", error);\n        });\n    }, delay);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/cacheUtils.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/serverTimestamp.ts":
/*!**************************************!*\
  !*** ./src/utils/serverTimestamp.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerTimestamp: () => (/* binding */ getServerTimestamp)\n/* harmony export */ });\n/**\n * This utility manages the server timestamp used to invalidate sessions on server restart\n */ // Use a more stable identifier that doesn't change on every development hot reload\n// In production, this will still change on server restart\nlet SERVER_START_TIMESTAMP;\n// Try to use a timestamp that persists across hot reloads in development\nif (true) {\n    // In development, use a timestamp that changes daily instead of on every restart\n    // This prevents constant logouts during development\n    const today = new Date();\n    const dateString = `${today.getFullYear()}-${today.getMonth()}-${today.getDate()}`;\n    SERVER_START_TIMESTAMP = dateString;\n} else {}\n// Function to get the current server timestamp\nfunction getServerTimestamp() {\n    return SERVER_START_TIMESTAMP;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/serverTimestamp.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/sessionToken.ts":
/*!***********************************!*\
  !*** ./src/utils/sessionToken.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupAllExpiredSessions: () => (/* binding */ cleanupAllExpiredSessions),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   removeAllOtherSessions: () => (/* binding */ removeAllOtherSessions),\n/* harmony export */   removeSession: () => (/* binding */ removeSession),\n/* harmony export */   validateSession: () => (/* binding */ validateSession)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n// Maximum number of sessions per user\nconst MAX_SESSIONS_PER_USER = 5;\n// Session expiration time in milliseconds (24 hours)\nconst SESSION_EXPIRATION_MS = 24 * 60 * 60 * 1000;\n/**\n * Generate a unique session token\n * @returns A random session token\n */ function generateSessionToken() {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes)(32).toString(\"hex\");\n}\n/**\n * Create a new session for a user\n * @param userId The user ID\n * @param userAgent The user agent string\n * @param ipAddress The IP address\n * @returns The session token\n */ async function createSession(userId, userAgent, ipAddress) {\n    const sessionToken = generateSessionToken();\n    // For now, just return the session token\n    // TODO: Implement proper session management with Prisma\n    console.log(`Created session for user ${userId}: ${sessionToken}`);\n    return sessionToken;\n}\n/**\n * Clean up expired sessions for a user\n * @param userId The user ID\n */ async function cleanupExpiredSessions(userId) {\n    // TODO: Implement with Prisma\n    console.log(`Cleaning up expired sessions for user ${userId}`);\n}\n/**\n * Validate a session token for a user\n * @param userId The user ID\n * @param sessionToken The session token to validate\n * @returns True if the session is valid, false otherwise\n */ async function validateSession(userId, sessionToken) {\n    // TODO: Implement with Prisma\n    console.log(`Validating session for user ${userId}: ${sessionToken}`);\n    return true; // For now, always return true\n}\n/**\n * Remove a session for a user\n * @param userId The user ID\n * @param sessionToken The session token to remove\n */ async function removeSession(userId, sessionToken) {\n    // TODO: Implement with Prisma\n    console.log(`Removing session for user ${userId}: ${sessionToken}`);\n}\n/**\n * Get all active sessions for a user\n * @param userId The user ID\n * @returns Array of active sessions\n */ async function getUserSessions(userId) {\n    // TODO: Implement with Prisma\n    console.log(`Getting sessions for user ${userId}`);\n    return [];\n}\n/**\n * Remove all sessions for a user except the current one\n * @param userId The user ID\n * @param currentSessionToken The current session token to keep\n * @returns The number of sessions removed\n */ async function removeAllOtherSessions(userId, currentSessionToken) {\n    await dbConnect();\n    // First clean up expired sessions\n    await cleanupExpiredSessions(userId);\n    // Get the user to count sessions before removal\n    const userBefore = await User.findById(userId);\n    const sessionCountBefore = userBefore?.activeSessions?.length || 0;\n    // Remove all other sessions\n    const result = await User.updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: {\n                    $ne: currentSessionToken\n                }\n            }\n        }\n    });\n    // Get the user again to count sessions after removal\n    const userAfter = await User.findById(userId);\n    const sessionCountAfter = userAfter?.activeSessions?.length || 0;\n    // Return the number of sessions removed\n    return sessionCountBefore - sessionCountAfter;\n}\n/**\n * Cleanup all expired sessions in the database\n * This can be run as a scheduled task\n */ async function cleanupAllExpiredSessions() {\n    await dbConnect();\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    // Find all users with expired sessions\n    const users = await User.find({\n        \"activeSessions.lastActive\": {\n            $lt: expirationThreshold\n        }\n    });\n    let totalRemoved = 0;\n    // Clean up expired sessions for each user\n    for (const user of users){\n        const sessionCountBefore = user.activeSessions.length;\n        // Remove expired sessions\n        await User.updateOne({\n            _id: user._id\n        }, {\n            $pull: {\n                activeSessions: {\n                    lastActive: {\n                        $lt: expirationThreshold\n                    }\n                }\n            }\n        });\n        // Get the user again to count sessions after removal\n        const updatedUser = await User.findById(user._id);\n        const sessionCountAfter = updatedUser?.activeSessions?.length || 0;\n        totalRemoved += sessionCountBefore - sessionCountAfter;\n    }\n    return totalRemoved;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/sessionToken.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/@babel","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/uuid","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMGB_2%20Workstation%5CDesktop%5CDocumentTracker%5CDocTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();